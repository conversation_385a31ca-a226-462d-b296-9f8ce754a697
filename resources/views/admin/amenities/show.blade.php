@extends('layouts.admin')

@section('title', 'Amenity Details - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Amenity Details - {{ $amenity->name }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.amenities.index') }}">Amenities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Details</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON>er Close -->

    <div class="row">
        <div class="col-xl-12">
            <!-- Success/Error Messages -->
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Amenity Information</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.amenities.edit', $amenity) }}" class="btn btn-sm btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit Amenity
                        </a>
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                            data-bs-target="#deleteModal">
                            <i class="ri-delete-bin-line me-1"></i>Delete Amenity
                        </button>
                        <a href="{{ route('admin.amenities.index') }}" class="btn btn-sm btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Amenities
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Name:</strong></div>
                                        <div class="col-sm-8">{{ $amenity->name }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Description:</strong></div>
                                        <div class="col-sm-8">{{ $amenity->description ?: 'No description provided' }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Icon:</strong></div>
                                        <div class="col-sm-8">
                                            <i class="{{ $amenity->icon_class }}" style="font-size: 1.5rem;"></i>
                                            <code class="ms-2">{{ $amenity->icon_class }}</code>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Status:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge {{ $amenity->status_badge_class }}">
                                                {{ $amenity->status_text }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Usage Statistics -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Usage Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Fields Using:</strong></div>
                                        <div class="col-sm-8">{{ $amenity->fields->count() }} fields</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Created:</strong></div>
                                        <div class="col-sm-8">{{ $amenity->created_at->format('M d, Y H:i') }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Last Updated:</strong></div>
                                        <div class="col-sm-8">{{ $amenity->updated_at->format('M d, Y H:i') }}</div>
                                    </div>
                                    @if ($amenity->deleted_at)
                                        <div class="row mb-3">
                                            <div class="col-sm-4"><strong>Deleted:</strong></div>
                                            <div class="col-sm-8 text-danger">
                                                {{ $amenity->deleted_at->format('M d, Y H:i') }}</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Associated Fields -->
                    @if ($amenity->fields->count() > 0)
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Associated Fields ({{ $amenity->fields->count() }})
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Field Name</th>
                                                        <th>Type</th>
                                                        <th>Status</th>
                                                        <th>Hourly Rate</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($amenity->fields as $field)
                                                        <tr>
                                                            <td>{{ $field->name }}</td>
                                                            <td><span class="badge bg-primary">{{ $field->type }}</span>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="badge
                                                                @if ($field->status === 'Active') bg-success
                                                                @elseif($field->status === 'Under Maintenance') bg-warning
                                                                @else bg-danger @endif">
                                                                    {{ $field->status }}
                                                                </span>
                                                            </td>
                                                            <td>${{ number_format($field->hourly_rate, 2) }}</td>
                                                            <td>
                                                                <a href="{{ route('admin.fields.show', $field) }}"
                                                                    class="btn btn-sm btn-info">
                                                                    <i class="ri-eye-line"></i>
                                                                </a>
                                                                <a href="{{ route('admin.fields.edit', $field) }}"
                                                                    class="btn btn-sm btn-primary">
                                                                    <i class="ri-edit-line"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">No Associated Fields</h6>
                                    <p class="mb-0">This amenity is not currently being used by any fields.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Amenity</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="ri-error-warning-line text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p>Are you sure you want to delete <strong>{{ $amenity->name }}</strong>?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                        @if ($amenity->fields->count() > 0)
                            <div class="alert alert-warning">
                                <strong>Warning:</strong> This amenity is currently used by {{ $amenity->fields->count() }}
                                field(s).
                                Deleting it will remove it from all associated fields.
                            </div>
                        @endif
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.amenities.destroy', $amenity) }}" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Amenity</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
