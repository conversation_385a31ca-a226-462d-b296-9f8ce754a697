@extends('layouts.admin')

@section('title', 'Create New Utility - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Create New Utility</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.utilities.index') }}">Utilities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">
                        Create New Utility
                    </div>
                    <div class="card-options">
                        <a href="{{ route('admin.utilities.index') }}" class="btn btn-light btn-sm">
                            <i class="ri-arrow-left-line me-1"></i>Back to Utilities
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.utilities.store') }}">
                        @csrf

                        <!-- Utility Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">Utility Name <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                class="form-control @error('name') is-invalid @enderror"
                                placeholder="e.g., Electricity, Water, Internet">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea name="description" id="description" rows="4"
                                class="form-control @error('description') is-invalid @enderror"
                                placeholder="Describe the utility and its purpose...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: Provide a detailed description of the utility.</div>
                        </div>



                        <!-- Hourly Rate -->
                        <div class="mb-3">
                            <label for="hourly_rate" class="form-label">Hourly Rate</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" name="hourly_rate" id="hourly_rate" value="{{ old('hourly_rate') }}"
                                    step="0.01" min="0" max="999999.99"
                                    class="form-control @error('hourly_rate') is-invalid @enderror" placeholder="0.00">
                                @error('hourly_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-text">Optional: Set the hourly rate for this utility (leave blank if not
                                applicable)</div>
                        </div>

                        <!-------------------------------------------------------------------------------------->
                        <!-- icons class -->
                        <div class="mb-3">
                            <label class="form-label">Select Icon <span class="text-danger">*</span></label>

                            <div class="d-flex flex-wrap gap-3" id="icon-picker">
                                @php
                                    $cssPath = public_path('assets\icon-fonts\RemixIcons\fonts\remixicon.css');
                                    $icons = [];

                                    if (file_exists($cssPath)) {
                                        $css = file_get_contents($cssPath);
                                        preg_match_all('/\.((ri-[a-z0-9\-]+-line))\:before/', $css, $matches);
                                        $icons = array_unique($matches[1]);
                                        sort($icons); // Optional: sort alphabetically
                                    }
                                    $selected = old('icon_class', 'ri-check-line');
                                @endphp

                                @foreach ($icons as $icon)
                                    <div class="icon-option border rounded p-2 {{ $selected === $icon ? 'border-primary' : '' }}"
                                        style="cursor: pointer;" data-icon="{{ $icon }}">
                                        <i class="{{ $icon }} fs-4"></i>
                                    </div>
                                @endforeach
                            </div>

                            <input type="hidden" name="icon_class" id="icon_class" value="{{ $selected }}">
                            @error('icon_class')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror

                            <div class="form-text">Click an icon to select it.</div>
                        </div>
                        <!-------------------------------------------------------------------------------------->


                        <!-- Form Actions -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Create Utility
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="ri-refresh-line me-1"></i>Reset Form
                                        </button>
                                    </div>
                                    <a href="{{ route('admin.utilities.index') }}" class="btn btn-outline-secondary">
                                        <i class="ri-close-line me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const name = document.getElementById('name').value.trim();
                const iconClass = document.getElementById('icon_class').value.trim();

                if (!name) {
                    e.preventDefault();
                    alert('Please enter a utility name.');
                    document.getElementById('name').focus();
                    return;
                }

                if (!iconClass) {
                    e.preventDefault();
                    alert('Please enter an icon class.');
                    document.getElementById('icon_class').focus();
                    return;
                }
            });

            // Auto-focus on name field
            document.getElementById('name').focus();
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iconOptions = document.querySelectorAll('.icon-option');
            const iconInput = document.getElementById('icon_class');

            iconOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active style
                    iconOptions.forEach(opt => opt.classList.remove('border-primary'));
                    // Add active style
                    this.classList.add('border-primary');
                    // Set hidden input
                    iconInput.value = this.getAttribute('data-icon');
                });
            });
        });
    </script>
@endpush
